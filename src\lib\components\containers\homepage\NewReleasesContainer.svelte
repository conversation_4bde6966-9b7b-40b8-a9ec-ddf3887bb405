<script>
	import { onMount, afterUpdate } from 'svelte';
	import NewRelease from '$lib/components/sections/homepage/NewRelease.svelte';
	import { tweened, spring } from 'svelte/motion';
	import { linear } from 'svelte/easing';
	import { ChevronDown, ChevronUp, List } from 'lucide-svelte';
	import { fade } from 'svelte/transition';
	import { generateAnimeUrl } from '$lib/myUtils';
	import { Button } from '$lib/components/ui/button';
	import * as Sheet from '$lib/components/ui/sheet';
	import * as Drawer from '$lib/components/ui/drawer';
	import { browser } from '$app/environment';

	// Get data from parent
	export let data, userSettings;
	let preferRomaji;
	if (!userSettings?.titleLanguage) {
		preferRomaji = true;
	} else {
		preferRomaji = userSettings.titleLanguage === 'romaji';
	}

	const FADE_DURATION = 250;
	let listenersInitialized = false;
	let initialRender = true;

	// Use server-provided data directly and create stacks
	$: rawNewReleases = data.newReleasesData;
	$: translatingGroups = data.translatingGroups || [];
	$: newReleases = createStacks(rawNewReleases);

	let isFading = false;
	let containerRef;
	let carouselRef;
	let isVerticalView = false;
	let containerHeight = 0;
	let isDragging = false;
	let startX;
	let startScrollLeft;
	let lastScrollLeft = 0;
	let lastMouseX = 0;
	let velocityX = 0;
	let lastTimestamp = 0;
	let animationFrameId = null;
	let maxScroll = 0;
	let dragDistance = 0;
	let isOverscrolling = false;
	let lastMouseY = 0;
	let initialTouchY;
	let isDraggingStarted = false;
	let isVerticalScroll = false;
	let startY = 0;

	// Stack management
	let expandedStacks = new Set();
	let fadingStacks = new Set(); // Tracks stacks that are fading out
	let hoverTimeouts = new Map();

	// Sheet/Drawer state
	let showAllEpisodes = false;
	let isMobile = false;

	// Create stacks by grouping episodes with same anilist_id + episode_number
	function createStacks(episodes) {
		if (!episodes || episodes.length === 0) return [];

		const stackMap = new Map();

		episodes.forEach(episode => {
			const stackKey = `${episode.anilist_id}-${episode.episode}`;

			if (!stackMap.has(stackKey)) {
				stackMap.set(stackKey, {
					...episode,
					stackKey,
					stackedEpisodes: [],
					isStack: false
				});
			}

			const stack = stackMap.get(stackKey);
			stack.stackedEpisodes.push(episode);

			// If we have more than one episode, mark as stack and use most recent as primary
			if (stack.stackedEpisodes.length > 1) {
				stack.isStack = true;
				// Sort by date_added (most recent first) and use the first one as primary display
				stack.stackedEpisodes.sort((a, b) => new Date(b.duration) - new Date(a.duration));
				// Update the primary episode data with the most recent
				Object.assign(stack, stack.stackedEpisodes[0]);
			}
		});

		// Convert map to array and sort by date (most recent first)
		return Array.from(stackMap.values()).sort((a, b) => new Date(b.duration) - new Date(a.duration));
	}

	function showStack(stackKey) {
		// Clear any pending hide timeout
		if (hoverTimeouts.has(stackKey)) {
			clearTimeout(hoverTimeouts.get(stackKey));
			hoverTimeouts.delete(stackKey);
		}

		// Remove from fading stacks if it was fading out
		fadingStacks.delete(stackKey);
		fadingStacks = fadingStacks;

		expandedStacks.add(stackKey);
		expandedStacks = expandedStacks; // Trigger reactivity
	}

	function hideStack(stackKey) {
		// Set a timeout to hide the stack, allowing time to move to the expanded list
		const timeout = setTimeout(() => {
			// Start fade out animation
			fadingStacks.add(stackKey);
			fadingStacks = fadingStacks;

			// Remove from expanded stacks after fade animation completes
			setTimeout(() => {
				expandedStacks.delete(stackKey);
				fadingStacks.delete(stackKey);
				expandedStacks = expandedStacks;
				fadingStacks = fadingStacks;
			}, 200); // Match the fade-out animation duration

			hoverTimeouts.delete(stackKey);
		}, 300); // Increased delay for better UX

		hoverTimeouts.set(stackKey, timeout);
	}

	// Polish declination helper
	function getPolishVersionText(count) {
		if (count === 1) return '1 wersja dostępna';
		if (count >= 2 && count <= 4) return `${count} wersje dostępne`;
		return `${count} wersji dostępnych`;
	}

	const DRAG_THRESHOLD = 5;
	const rows = 3;
	let itemWidth = 240;
	let itemHeight = 140;

	function calculateInitialHeight() {
		if (checkMobile()) {
			itemWidth = 240;
			itemHeight = 110;
		}
		const baseHeight = itemHeight * rows;
		const extraPadding = 60;
		return isVerticalView ? baseHeight : baseHeight / (window.innerWidth >= 768 ? 1.7 : 2.3) + extraPadding;
	}

	const containerHeightTweened = tweened(calculateInitialHeight(), {
		duration: 0,
		easing: linear
	});

	$: if (!initialRender) {
		if (isVerticalView) {
			containerHeightTweened.set(containerHeight + 20, { duration: 400 });
		} else {
			containerHeightTweened.set(containerHeight / (window.innerWidth >= 768 ? 1.7 : 2.3) + 20, { duration: 400 });
		}
	}

	const leftOverscroll = spring(0, {
		stiffness: 0.1,
		damping: 0.9
	});

	const rightOverscroll = spring(0, {
		stiffness: 0.1,
		damping: 0.9
	});

	function initializeEventListeners() {
		if (listenersInitialized) return;

		window.addEventListener('mousemove', handleDragMove);
		window.addEventListener('mouseup', handleDragEnd);
		window.addEventListener('touchmove', handleDragMove, { passive: false });
		window.addEventListener('touchend', handleDragEnd);

		listenersInitialized = true;
	}

	function getEventCoords(e) {
		return {
			x: e.type.includes('mouse') ? e.pageX : e.touches[0].pageX,
			y: e.type.includes('mouse') ? e.pageY : e.touches[0].pageY
		};
	}

	function handleDragStart(e) {
		if (!carouselRef || isVerticalView) return;

		isDragging = true;
		const coords = getEventCoords(e);
		startX = coords.x;
		startY = coords.y;
		initialTouchY = coords.y;
		// Use lastScrollLeft as the starting position instead of carouselRef.scrollLeft
		startScrollLeft = lastScrollLeft || 0;
		lastMouseX = startX;
		lastTimestamp = Date.now();
		velocityX = 0;
		dragDistance = 0;
		isVerticalScroll = false;

		if (animationFrameId) {
			cancelAnimationFrame(animationFrameId);
		}

		if (carouselRef) {
			carouselRef.style.scrollBehavior = 'auto';
			carouselRef.style.cursor = 'grabbing';
		}

		// Hide all expanded stacks during dragging
		expandedStacks.clear();
		expandedStacks = expandedStacks;
	}

	function handleDragMove(e) {
		if (!isDragging || !carouselRef || isVerticalView) return;

		// Recalculate maxScroll to ensure it's up to date
		maxScroll = carouselRef.scrollWidth - carouselRef.clientWidth;

		const coords = getEventCoords(e);
		const deltaX = Math.abs(coords.x - startX);
		const deltaY = Math.abs(coords.y - initialTouchY);

		if (!isVerticalScroll && deltaY > deltaX && deltaY > 10) {
			isVerticalScroll = true;
			handleDragEnd(e);
			return;
		}

		if (isVerticalScroll) return;

		if (deltaX > deltaY) {
			e.preventDefault();
		}

		const currentTimestamp = Date.now();
		const timeElapsed = currentTimestamp - lastTimestamp;

		const moveX = coords.x - lastMouseX;
		dragDistance += Math.abs(moveX);

		if (timeElapsed > 0) {
			// Calculate instantaneous velocity
			const instantVelocity = moveX / timeElapsed;

			// Use a weighted average to smooth velocity but still capture quick movements
			// Give more weight to recent movement for better quick flick detection
			if (velocityX === 0) {
				velocityX = instantVelocity;
			} else {
				velocityX = velocityX * 0.3 + instantVelocity * 0.7;
			}
		}

		const walkX = startX - coords.x;
		let newScrollLeft = startScrollLeft + walkX;

		if (newScrollLeft < 0) {
			isOverscrolling = true;
			leftOverscroll.set(-newScrollLeft * 0.3, { duration: 0 });
			rightOverscroll.set(0, { duration: 0 });
			newScrollLeft = 0;
		} else if (newScrollLeft > maxScroll) {
			isOverscrolling = true;
			rightOverscroll.set((newScrollLeft - maxScroll) * 0.3, { duration: 0 });
			leftOverscroll.set(-maxScroll, { duration: 0 });
			newScrollLeft = maxScroll;
		} else {
			isOverscrolling = false;
			// For normal scrolling, use leftOverscroll to create the scroll effect
			leftOverscroll.set(-newScrollLeft, { duration: 0 });
			rightOverscroll.set(0, { duration: 0 });
		}

		lastScrollLeft = newScrollLeft;

		lastMouseX = coords.x;
		lastTimestamp = currentTimestamp;
	}

	function handleDragEnd(e) {
		if (!isDragging || !carouselRef || isVerticalView) return;
		isDragging = false;

		if (carouselRef) {
			carouselRef.style.cursor = 'grab';
		}

		if (isOverscrolling) {
			isOverscrolling = false;
			// Let springs animate back naturally for floaty feel
			leftOverscroll.set(0);
			rightOverscroll.set(0);
		}

		if (dragDistance > DRAG_THRESHOLD) {
			e.preventDefault();
			e.stopPropagation();

			const clickPreventionHandler = (e) => {
				e.preventDefault();
				e.stopPropagation();
			};

			carouselRef.addEventListener('click', clickPreventionHandler, { capture: true });
			setTimeout(() => {
				if (carouselRef) {
					carouselRef.removeEventListener('click', clickPreventionHandler, { capture: true });
				}
			}, 100);
		}

		if (carouselRef) {
			// Lower threshold for momentum and consider drag distance for quick flicks
			const isQuickFlick = dragDistance > 20 && Math.abs(velocityX) > 0.05;
			const hasSignificantVelocity = Math.abs(velocityX) > 0.02;

			if (isQuickFlick || hasSignificantVelocity) {
				applyMomentumScroll();
			} else {
				carouselRef.style.scrollBehavior = 'smooth';
			}
		}
	}

	function applyMomentumScroll() {
		if (Math.abs(velocityX) < 0.005) {
			carouselRef.style.scrollBehavior = 'smooth';
			return;
		}

		// Recalculate maxScroll to ensure it's up to date
		maxScroll = carouselRef.scrollWidth - carouselRef.clientWidth;

		const currentScroll = lastScrollLeft || 0;

		// Increase momentum multiplier for more responsive feel
		// Scale based on velocity magnitude for better quick flick response
		const velocityMagnitude = Math.abs(velocityX);
		let momentumMultiplier = 20; // Base multiplier

		// Boost momentum for quick flicks
		if (velocityMagnitude > 0.5) {
			momentumMultiplier = 35;
		} else if (velocityMagnitude > 0.2) {
			momentumMultiplier = 28;
		}

		let newScroll = currentScroll - velocityX * momentumMultiplier;

		if (newScroll < 0) {
			newScroll = 0;
			velocityX = Math.abs(velocityX) * 0.3; // Slightly more bounce
		} else if (newScroll > maxScroll) {
			newScroll = maxScroll;
			velocityX = -Math.abs(velocityX) * 0.3; // Slightly more bounce
		}

		// Update with slight spring animation for smoother momentum
		leftOverscroll.set(-newScroll, { duration: 50 });
		rightOverscroll.set(0, { duration: 50 });
		lastScrollLeft = newScroll;

		// Adjust deceleration based on velocity for more natural feel
		const decelerationRate = velocityMagnitude > 0.3 ? 0.92 : 0.94;
		velocityX *= decelerationRate;

		animationFrameId = requestAnimationFrame(applyMomentumScroll);
	}

	function checkMobile() {
		return window.innerWidth < 1024;
	}

	function calculateGrid() {
		if (checkMobile()) {
			itemWidth = 240;
			itemHeight = 110;
		} else {
			itemWidth = 240;
			itemHeight = 140;
		}
		if (containerRef) {
			containerHeight = itemHeight * rows;
		}
	}

	function setupScrollBehavior() {
		if (!carouselRef) return;

		// Use requestAnimationFrame to ensure DOM is fully rendered
		requestAnimationFrame(() => {
			if (!carouselRef) return;
			maxScroll = carouselRef.scrollWidth - carouselRef.clientWidth;

			// Ensure lastScrollLeft is synced with current visual position
			const currentTransform = -$leftOverscroll;
			if (currentTransform !== lastScrollLeft) {
				lastScrollLeft = currentTransform;
			}
		});

		carouselRef.removeEventListener('mousedown', handleDragStart);
		carouselRef.removeEventListener('touchstart', handleDragStart);
		carouselRef.removeEventListener('wheel', handleWheel);

		if (!isVerticalView) {
			carouselRef.addEventListener('mousedown', handleDragStart);
			carouselRef.addEventListener('touchstart', handleDragStart, { passive: true });
			carouselRef.addEventListener('wheel', handleWheel, { passive: false });
			carouselRef.style.cursor = 'grab';
		} else {
			carouselRef.style.cursor = 'default';
		}
	}

	async function toggleView() {
		if (initialRender) {
			initialRender = false;
			return;
		}

		isFading = true;
		await new Promise((resolve) => setTimeout(resolve, FADE_DURATION / 2));

		const isMobile = checkMobile();
		const containerWidth = containerRef?.clientWidth || window.innerWidth;

		if (isMobile) {
			const gapWidth = 14;
			const cardWidth = Math.floor((containerWidth - gapWidth - 28) / 2);
			itemWidth = cardWidth;
			itemHeight = Math.floor(cardWidth * 0.458);
		} else {
			itemWidth = 240;
			itemHeight = 140;
		}

		isVerticalView = !isVerticalView;

		if (animationFrameId) {
			cancelAnimationFrame(animationFrameId);
		}

		if (carouselRef) {
			carouselRef.style.scrollBehavior = 'auto';
			carouselRef.scrollLeft = 0;
			carouselRef.scrollTop = 0;
			leftOverscroll.set(0);
			rightOverscroll.set(0);
			lastScrollLeft = 0;
		}

		calculateGrid();
		setupScrollBehavior();

		await new Promise((resolve) => setTimeout(resolve, FADE_DURATION / 2));
		isFading = false;

		requestAnimationFrame(() => {
			forceLoadVisibleImages();
			if (carouselRef) {
				carouselRef.style.scrollBehavior = 'smooth';
			}
		});
	}

	function lazyLoadImages(forceLoad = false) {
		if (containerRef) {
			const images = containerRef.querySelectorAll('img[data-src]');
			const containerRect = containerRef.getBoundingClientRect();

			images.forEach((img) => {
				const imgRect = img.getBoundingClientRect();
				if (forceLoad || (imgRect.left < containerRect.right + 200 && imgRect.right > containerRect.left - 200)) {
					if (img.dataset.src && img.src !== img.dataset.src) {
						img.src = img.dataset.src;
						img.removeAttribute('data-src');
					}
				}
			});
		}
	}

	function forceLoadVisibleImages() {
		lazyLoadImages(true);
	}

	function handleScroll() {
		requestAnimationFrame(lazyLoadImages);
	}



	function handleWheel(e) {
		if (!carouselRef || isVerticalView) return;

		// Handle horizontal scrolling with shift+scroll or horizontal wheel
		if (e.shiftKey || Math.abs(e.deltaX) > Math.abs(e.deltaY)) {
			e.preventDefault();
			let delta = e.deltaX || e.deltaY;

			// Make shift+scroll more sensitive by reducing the delta
			// Standard wheel delta is usually 100, reduce it for finer control
			delta = delta * 3; // Much more sensitive scrolling

			// Recalculate maxScroll to ensure it's up to date
			maxScroll = carouselRef.scrollWidth - carouselRef.clientWidth;

			// Get current scroll position from the spring value or lastScrollLeft
			const currentScrollLeft = lastScrollLeft !== undefined ? lastScrollLeft : 0;
			let newScrollLeft = currentScrollLeft + delta;

			// Clamp the scroll position
			if (newScrollLeft < 0) {
				newScrollLeft = 0;
			} else if (newScrollLeft > maxScroll) {
				newScrollLeft = maxScroll;
			}

			// Update the transform via spring values with slight animation
			leftOverscroll.set(-newScrollLeft, { duration: 50 });
			rightOverscroll.set(0, { duration: 50 });
			lastScrollLeft = newScrollLeft;
		}
	}

	function handleResize() {
		calculateGrid();
		setupScrollBehavior();
		lazyLoadImages();
	}

	function getKey(episode, index) {
		return `${episode.id}-${index}`;
	}

	onMount(() => {
		calculateGrid();
		setupScrollBehavior();
		initializeEventListeners();
		window.addEventListener('resize', handleResize);
		carouselRef?.addEventListener('scroll', handleScroll, { passive: true });
		carouselRef?.addEventListener('wheel', handleWheel, { passive: false });

		// Initialize scroll position properly
		requestAnimationFrame(() => {
			if (carouselRef) {
				// Sync lastScrollLeft with current spring position
				const currentTransform = -$leftOverscroll;
				lastScrollLeft = currentTransform;
			}
		});

		// Set initial height without animation
		containerHeightTweened.set(calculateInitialHeight(), { duration: 0 });

		setTimeout(() => {
			initialRender = false;
		}, 0);

		return () => {
			if (animationFrameId) {
				cancelAnimationFrame(animationFrameId);
			}
			window.removeEventListener('resize', handleResize);
			carouselRef?.removeEventListener('scroll', handleScroll);
			carouselRef?.removeEventListener('wheel', handleWheel);


			if (listenersInitialized) {
				window.removeEventListener('mousemove', handleDragMove);
				window.removeEventListener('mouseup', handleDragEnd);
				window.removeEventListener('touchmove', handleDragMove);
				window.removeEventListener('touchend', handleDragEnd);
			}
		};
	});

	afterUpdate(() => {
		if (carouselRef) {
			setupScrollBehavior();
		}
		forceLoadVisibleImages();
	});

	$: if (newReleases) {
		setTimeout(forceLoadVisibleImages, 0);
	}

	// Mobile detection for sheet/drawer - based on screen dimensions only
	function isMobileDevice() {
		if (!browser) return false;
		return window.innerWidth < 768;
	}

	// Handle opening the all episodes view
	function handleShowAllEpisodes() {
		isMobile = isMobileDevice();
		showAllEpisodes = true;
	}

	onMount(() => {
		isMobile = isMobileDevice();
		if (browser) {
			window.addEventListener('resize', () => {
				isMobile = isMobileDevice();
			});
		}
	});
</script>

<section class="new-releases mt-8 pt-8 {isVerticalView ? 'mb-12' : '-mb-12'} {!initialRender ? 'transition-all duration-500 ease-in-out' : ''}">
	<div class="flex items-center justify-between pl-4 pr-4 mb-4 md:pr-8">
		<h2 class="text-xl font-bold text-white opacity-80 md:text-3xl">Nowości</h2>
		<div class="flex items-center gap-2">
			<Button
				variant="outline"
				size={isMobileDevice() ? "sm" : "sm"}
				on:click={handleShowAllEpisodes}
				class="text-white border-gray-600 cursor-pointer hover:bg-gray-700 hover:text-white {isMobileDevice() ? 'text-xs px-2 py-1 h-7' : ''}"
			>
				<List size={isMobileDevice() ? 12 : 16} class={isMobileDevice() ? "mr-1" : "mr-2"} />
				{isMobileDevice() ? "Wszystkie" : "Zobacz wszystkie"}
			</Button>
			<button on:click={toggleView} class="p-2 text-white bg-gray-700 rounded-full cursor-pointer hover:bg-gray-600">
				{#if isVerticalView}
					<ChevronUp size={24} />
				{:else}
					<ChevronDown size={24} />
				{/if}
			</button>
		</div>
	</div>

	<div class="{!initialRender ? 'transition-[height] duration-400' : ''} relative overflow-visible" style="height: {$containerHeightTweened}px">
		<div class="relative w-full h-full">
			{#if !isFading}
				<div class="absolute inset-0" transition:fade|local={{ duration: initialRender ? 0 : FADE_DURATION }}>
					<div class="carousel-container" class:is-vertical={isVerticalView} bind:this={containerRef}>
						<div class="carousel-content" class:vertical={isVerticalView} bind:this={carouselRef} style="transform: translate3d({!isVerticalView ? $leftOverscroll - $rightOverscroll : 0}px, 0, 0);">
							<div class="carousel-slides">
								{#each newReleases as episode, index (getKey(episode, index))}
									<div class="relative carousel-slide">
										{#if episode.isStack}
											<!-- Stack container with persistent hover zone -->
											<div class="relative stack-container"
												 role="button"
												 tabindex="0"
												 aria-label="Stack of {episode.stackedEpisodes.length} episode versions">

												<!-- Expanded stack (compressed list format) - extends upward from card -->
												{#if expandedStacks.has(episode.stackKey)}
													<div class="expanded-stack absolute bottom-full left-0 right-0 z-20 mb-1 bg-gray-800/95 backdrop-blur-sm rounded-lg border border-gray-700 shadow-xl p-3 min-w-[280px] {fadingStacks.has(episode.stackKey) ? 'animate-fade-out' : 'animate-slide-up'}"
														 role="menu"
														 tabindex="0"
														 aria-label="Available episode versions"
														 on:mouseenter={() => !isDragging && showStack(episode.stackKey)}
														 on:mouseleave={() => !isDragging && hideStack(episode.stackKey)}>
														<div class="mb-2 text-xs font-medium text-gray-300">
															{getPolishVersionText(episode.stackedEpisodes.length)}:
														</div>
														<div class="space-y-1">
															{#each episode.stackedEpisodes as stackedEpisode, stackIndex}
																<a href={`${generateAnimeUrl(stackedEpisode)}/watch/${stackedEpisode.episode}`}
																   class="flex items-center gap-3 p-2 transition-colors rounded-md cursor-pointer hover:bg-gray-700/50 group">
																	<!-- Group logo/icon -->
																	<div class="flex-shrink-0">
																		{#if translatingGroups.find(g => g.name === stackedEpisode.translating_group)?.logo_url}
																			<img src={translatingGroups.find(g => g.name === stackedEpisode.translating_group).logo_url}
																				 alt="{stackedEpisode.translating_group} logo"
																				 class="object-cover w-6 h-6 rounded-full" />
																		{:else}
																			<div class="flex items-center justify-center w-6 h-6 text-xs font-bold text-white bg-gray-600 rounded-full">
																				{(stackedEpisode.translating_group || 'L').charAt(0).toUpperCase()}
																			</div>
																		{/if}
																	</div>

																	<!-- Group info -->
																	<div class="flex-1 min-w-0">
																		<div class="text-sm font-medium text-white transition-colors group-hover:text-blue-300">
																			{stackedEpisode.translating_group || 'lycoris.cafe'}
																		</div>
																		<div class="text-xs text-gray-400">
																			Dodano {new Date(stackedEpisode.duration).toLocaleDateString('pl-PL')}
																		</div>
																	</div>

																	<!-- Quality indicator -->
																	<div class="flex-shrink-0 px-2 py-1 text-xs text-gray-300 bg-gray-700 rounded">
																		HD
																	</div>
																</a>
															{/each}
														</div>
													</div>
												{/if}

												<!-- Primary episode (always visible, overlays the expanded list) -->
												<div class="relative z-30 primary-episode"
													 role="button"
													 tabindex="0"
													 aria-label="Show stack options"
													 on:mouseenter={() => !isDragging && showStack(episode.stackKey)}
													 on:mouseleave={() => !isDragging && hideStack(episode.stackKey)}>
													<NewRelease {preferRomaji} {episode} lazyLoad={index > 3} isVertical={isVerticalView} {isDragging} {translatingGroups} />
												</div>
											</div>
										{:else}
											<!-- Regular single episode -->
											<NewRelease {preferRomaji} {episode} lazyLoad={index > 3} isVertical={isVerticalView} {isDragging} {translatingGroups} />
										{/if}
									</div>
								{/each}
							</div>
						</div>
					</div>
				</div>
			{/if}
		</div>
	</div>
</section>

<!-- Desktop Sheet -->
{#if !isMobile}
	<Sheet.Root bind:open={showAllEpisodes}>
		<Sheet.Content side="right" class="w-[450px] sm:w-[600px] bg-gray-900 border-gray-700">
			<Sheet.Header>
				<Sheet.Title class="text-white">Wszystkie nowe odcinki</Sheet.Title>
				<Sheet.Description class="text-gray-400">
					Wszystkie najnowsze odcinki ze wszystkich grup tłumaczeniowych
				</Sheet.Description>
			</Sheet.Header>
			<div class="mt-6 space-y-4 overflow-y-auto max-h-[calc(100vh-120px)]">
				{#each rawNewReleases as episode}
					<a
						href={`${generateAnimeUrl(episode)}/watch/${episode.episode}`}
						class="flex items-center gap-3 p-3 transition-colors bg-gray-800 rounded-lg hover:bg-gray-700"
					>
						<img
							src={episode.image}
							alt={episode.title}
							class="object-cover w-16 h-12 rounded"
						/>
						<div class="flex-1 min-w-0">
							<h3 class="text-sm font-medium text-white truncate">
								{episode.title}
							</h3>
							<p class="text-xs text-gray-400">
								Odcinek {episode.episode} • {episode.translating_group === 'lycoris_cafe' ? 'lycoris.cafe' : episode.translating_group}
							</p>
						</div>
					</a>
				{/each}
			</div>
		</Sheet.Content>
	</Sheet.Root>
{/if}

<!-- Mobile Drawer -->
{#if isMobile}
	<Drawer.Root bind:open={showAllEpisodes}>
		<Drawer.Content class="bg-gray-900 border-gray-700 h-[85vh]">
			<div class="mx-auto w-12 h-1.5 flex-shrink-0 rounded-full bg-gray-600 mb-4" />
			<div class="px-4">
				<div class="space-y-3 overflow-y-auto max-h-[calc(85vh-60px)]">
					{#each rawNewReleases as episode}
						<a
							href={`${generateAnimeUrl(episode)}/watch/${episode.episode}`}
							class="flex items-center gap-3 p-3 transition-colors bg-gray-800 rounded-lg hover:bg-gray-700"
						>
							<img
								src={episode.image}
								alt={episode.title}
								class="object-cover w-16 h-12 rounded"
							/>
							<div class="flex-1 min-w-0">
								<h3 class="text-sm font-medium text-white truncate">
									{episode.title}
								</h3>
								<p class="text-xs text-gray-400">
									Odcinek {episode.episode} • {episode.translating_group === 'lycoris_cafe' ? 'lycoris.cafe' : episode.translating_group}
								</p>
							</div>
						</a>
					{/each}
				</div>
			</div>
		</Drawer.Content>
	</Drawer.Root>
{/if}

<style>
	.new-releases {
		position: relative;
		z-index: 10;
	}

	.carousel-container {
		width: 100%;
		height: calc(100% + 20px); /* Reduced from 40px */
		margin: -10px 0; /* Reduced from -20px */
		overflow-x: hidden;
		overflow-y: visible;
		-webkit-overflow-scrolling: touch;
		cursor: default;
		touch-action: pan-y pan-x;
		opacity: 1;
	}

	.carousel-container.is-vertical {
		overflow-y: auto;
		overflow-x: hidden;
		touch-action: pan-y;
	}

	.carousel-content {
		display: flex;
		overflow-x: auto;
		overflow-y: visible;
		scrollbar-width: none;
		-ms-overflow-style: none;
		-webkit-overflow-scrolling: touch;
		cursor: grab;
		user-select: none;
		touch-action: pan-y pan-x;
		width: 100%;
		padding: 10px 0; /* Reduced from 20px */
		scroll-behavior: smooth;
	}

	.carousel-content:active {
		cursor: grabbing;
	}

	.carousel-content::-webkit-scrollbar {
		display: none;
	}

	.carousel-content.vertical {
		flex-wrap: wrap;
		gap: 14px;
		padding: 14px;
		justify-content: center;
		align-items: flex-start;
		cursor: default;
		overflow-x: hidden;
		overflow-y: visible;
		touch-action: pan-y;
		transform: none !important;
	}

	.carousel-slides {
		display: flex;
		gap: 14px;
		padding: 0 14px;
		min-width: min-content;
		width: max-content;
	}

	.vertical .carousel-slides {
		width: 100%;
		flex-wrap: wrap;
		justify-content: center;
		padding: 0;
	}

	.carousel-slide {
		flex: 0 0 auto;
	}

	@media (min-width: 640px) {
		.carousel-slides {
			gap: 10px;
			padding: 0 10px;
		}

		.vertical .carousel-slides {
			padding: 0;
		}

		.carousel-container.is-vertical {
			scrollbar-width: thin;
			scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
		}

		.carousel-container.is-vertical::-webkit-scrollbar {
			display: block;
			width: 6px;
		}

		.carousel-container.is-vertical::-webkit-scrollbar-track {
			background: transparent;
		}

		.carousel-container.is-vertical::-webkit-scrollbar-thumb {
			background-color: rgba(255, 255, 255, 0.2);
			border-radius: 3px;
		}

		.carousel-container.is-vertical::-webkit-scrollbar-thumb:hover {
			background-color: rgba(255, 255, 255, 0.3);
		}
	}

	@media (min-width: 768px) {
		.carousel-slides {
			gap: 14px;
			padding: 0 14px;
		}

		.vertical .carousel-slides {
			padding: 0;
		}
	}

	.vertical .carousel-slide {
		width: calc(50% - 7px);
	}

	@media (min-width: 450px) {
		.vertical .carousel-slide {
			width: auto;
		}
	}

	/* Stack system styles */
	.stack-container {
		position: relative;
		overflow: visible;
	}

	/* Ensure stack containers don't interfere with scrolling */
	.stack-container {
		pointer-events: auto;
	}

	.stack-container:hover {
		pointer-events: auto;
	}

	.expanded-stack {
		pointer-events: auto; /* Enable pointer events for the expanded stack */
		z-index: 30;
		transform-origin: bottom center; /* Animation originates from bottom since it extends upward */
	}

	.animate-slide-up {
		animation: slideUp 0.2s ease-out forwards;
	}

	.animate-fade-out {
		animation: fadeOut 0.2s ease-out forwards;
	}

	@keyframes slideUp {
		from {
			opacity: 0;
			transform: translateY(10px) scaleY(0.8);
		}
		to {
			opacity: 1;
			transform: translateY(0) scaleY(1);
		}
	}

	@keyframes fadeOut {
		from {
			opacity: 1;
			transform: translateY(0) scaleY(1);
		}
		to {
			opacity: 0;
			transform: translateY(5px) scaleY(0.9);
		}
	}



	/* Ensure expanded stacks are visible over container boundaries */
	.carousel-container {
		overflow: visible;
	}

	.carousel-content {
		overflow: visible;
	}

	.carousel-slides {
		overflow: visible;
	}

	.carousel-slide {
		overflow: visible;
	}
</style>
